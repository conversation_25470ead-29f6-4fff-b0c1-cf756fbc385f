import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:somayya_academy/app/core/models/api_response.dart';
import 'package:somayya_academy/app/core/utils/password_encryptor.dart';
import 'package:somayya_academy/app/features/auth/controllers/auth_controller.dart';
import 'package:somayya_academy/app/features/auth/models/user_model.dart';
import 'package:somayya_academy/app/features/auth/services/auth_service.dart';
import 'package:somayya_academy/app/features/auth/services/social_auth_service.dart';

import '../../../../helpers/firebase_helpers.dart';
import 'auth_controller_test.mocks.dart';

// Generate mocks for dependencies
@GenerateMocks([AuthService, SocialAuthService, PasswordEncryptor])
void main() {
  group('AuthController', () {
    late AuthController authController;
    late MockAuthService mockAuthService;
    late MockSocialAuthService mockSocialAuthService;
    late MockPasswordEncryptor mockPasswordEncryptor;

    setUp(() {
      // Setup Firebase mocks
      setupFirebaseCoreMocks();

      // Clear GetX dependencies
      Get.reset();

      // Create mocks
      mockAuthService = MockAuthService();
      mockSocialAuthService = MockSocialAuthService();
      mockPasswordEncryptor = MockPasswordEncryptor();

      // Register mocked services
      Get.put<AuthService>(mockAuthService);
      Get.put<SocialAuthService>(mockSocialAuthService);

      authController = AuthController();
    });

    tearDown(() {
      Get.reset();
    });

    group('Initialization', () {
      test('initializes with default values', () {
        // Assert
        expect(authController.user.value, isNull);
        expect(authController.loading.value, isFalse);
        expect(authController.googleLoading.value, isFalse);
        expect(authController.facebookLoading.value, isFalse);
        expect(authController.microsoftLoading.value, isFalse);
      });

      test('calls _checkCurrentUser on initialization', () async {
        // Note: _checkCurrentUser is private, so we test its effects
        // In a real scenario, you might make it protected for testing

        // The controller should be initialized without errors
        expect(authController, isNotNull);
      });
    });

    group('Social Authentication', () {
      group('signInWithGoogle', () {
        test('sets googleLoading to true during sign-in', () async {
          // Arrange
          final successResponse = ApiResponse.success(
            data: {
              'token': 'token',
              'user': {'id': '1', 'email': '<EMAIL>'},
            },
          );
          when(
            mockSocialAuthService.signInWithGoogle(),
          ).thenAnswer((_) async => successResponse);

          // Act
          final future = authController.signInWithGoogle();

          // Assert - loading should be true during operation
          expect(authController.googleLoading.value, isTrue);

          await future;

          // Assert - loading should be false after operation
          expect(authController.googleLoading.value, isFalse);
        });

        test('updates user on successful Google sign-in', () async {
          // Arrange
          final userData = {
            'id': '1',
            'email': '<EMAIL>',
            'name': 'Test User',
          };
          final successResponse = ApiResponse.success(data: userData);
          when(
            mockSocialAuthService.signInWithGoogle(),
          ).thenAnswer((_) async => successResponse);

          // Act
          await authController.signInWithGoogle();

          // Assert
          expect(authController.user.value, isNotNull);
          expect(authController.googleLoading.value, isFalse);
        });

        test('handles Google sign-in failure gracefully', () async {
          // Arrange
          when(
            mockSocialAuthService.signInWithGoogle(),
          ).thenThrow(Exception('Google sign-in failed'));

          // Act & Assert
          await authController.signInWithGoogle();

          expect(authController.googleLoading.value, isFalse);
          expect(authController.user.value, isNull);
        });

        test('handles null response from Google sign-in', () async {
          // Arrange
          when(
            mockSocialAuthService.signInWithGoogle(),
          ).thenAnswer((_) async => null);

          // Act
          await authController.signInWithGoogle();

          // Assert
          expect(authController.googleLoading.value, isFalse);
          expect(authController.user.value, isNull);
        });
      });

      group('signInWithFacebook', () {
        test('sets facebookLoading to true during sign-in', () async {
          // Arrange
          final successResponse = ApiResponse.success(
            data: {
              'token': 'token',
              'user': {'id': '1', 'email': '<EMAIL>'},
            },
          );
          when(
            mockSocialAuthService.signInWithFacebook(),
          ).thenAnswer((_) async => successResponse);

          // Act
          final future = authController.signInWithFacebook();

          // Assert
          expect(authController.facebookLoading.value, isTrue);

          await future;
          expect(authController.facebookLoading.value, isFalse);
        });

        test('updates user on successful Facebook sign-in', () async {
          // Arrange
          final userData = {
            'id': '1',
            'email': '<EMAIL>',
            'name': 'Test User',
          };
          final successResponse = ApiResponse.success(data: userData);
          when(
            mockSocialAuthService.signInWithFacebook(),
          ).thenAnswer((_) async => successResponse);

          // Act
          await authController.signInWithFacebook();

          // Assert
          expect(authController.user.value, isNotNull);
          expect(authController.facebookLoading.value, isFalse);
        });
      });

      group('signInWithMicrosoft', () {
        test('sets microsoftLoading to true during sign-in', () async {
          // Arrange
          final successResponse = ApiResponse.success(
            data: {
              'token': 'token',
              'user': {'id': '1', 'email': '<EMAIL>'},
            },
          );
          when(
            mockSocialAuthService.signInWithMicrosoft(),
          ).thenAnswer((_) async => successResponse);

          // Act
          final future = authController.signInWithMicrosoft();

          // Assert
          expect(authController.microsoftLoading.value, isTrue);

          await future;
          expect(authController.microsoftLoading.value, isFalse);
        });

        test('updates user on successful Microsoft sign-in', () async {
          // Arrange
          final userData = {
            'id': '1',
            'email': '<EMAIL>',
            'name': 'Test User',
          };
          final successResponse = ApiResponse.success(data: userData);
          when(
            mockSocialAuthService.signInWithMicrosoft(),
          ).thenAnswer((_) async => successResponse);

          // Act
          await authController.signInWithMicrosoft();

          // Assert
          expect(authController.user.value, isNotNull);
          expect(authController.microsoftLoading.value, isFalse);
        });
      });
    });

    group('Email/Password Authentication', () {
      group('login', () {
        test('encrypts password and calls signin API', () async {
          // Arrange
          const email = '<EMAIL>';
          const password = 'password123';
          const encryptedPassword = 'encrypted_password';
          final userData = {
            'id': '1',
            'email': email,
            'name': 'Test User',
            'token': 'token',
          };
          final successResponse = ApiResponse.success(data: userData);

          // Mock password encryption
          when(
            mockPasswordEncryptor.encryptPassword(password),
          ).thenAnswer((_) async => encryptedPassword);

          when(
            mockAuthService.signin(email: email, password: encryptedPassword),
          ).thenAnswer((_) async => successResponse);

          // Act
          await authController.login(email, password);

          // Assert
          verify(
            mockAuthService.signin(email: email, password: encryptedPassword),
          ).called(1);
          expect(authController.user.value, isNotNull);
          expect(authController.loading.value, isFalse);
        });

        test('sets loading state during login', () async {
          // Arrange
          const email = '<EMAIL>';
          const password = 'password123';
          const encryptedPassword = 'encrypted_password';
          final successResponse = ApiResponse.success(data: {});

          when(
            mockPasswordEncryptor.encryptPassword(password),
          ).thenAnswer((_) async => encryptedPassword);
          when(
            mockAuthService.signin(email: email, password: encryptedPassword),
          ).thenAnswer((_) async => successResponse);

          // Act
          final future = authController.login(email, password);

          // Assert
          expect(authController.loading.value, isTrue);

          await future;
          expect(authController.loading.value, isFalse);
        });

        test('handles login failure gracefully', () async {
          // Arrange
          const email = '<EMAIL>';
          const password = 'wrongpassword';
          const encryptedPassword = 'encrypted_password';
          final errorResponse = ApiResponse.error(
            error: ApiError(message: 'Invalid credentials'),
          );

          when(
            mockPasswordEncryptor.encryptPassword(password),
          ).thenAnswer((_) async => encryptedPassword);
          when(
            mockAuthService.signin(email: email, password: encryptedPassword),
          ).thenAnswer((_) async => errorResponse);

          // Act
          await authController.login(email, password);

          // Assert
          expect(authController.loading.value, isFalse);
          expect(authController.user.value, isNull);
        });
      });

      group('requestOtp', () {
        test('calls sendEmailVerification API', () async {
          // Arrange
          const email = '<EMAIL>';
          final successResponse = ApiResponse.success(data: {});
          when(
            mockAuthService.sendEmailVerification(email: email),
          ).thenAnswer((_) async => successResponse);

          // Act
          await authController.requestOtp(email);

          // Assert
          verify(mockAuthService.sendEmailVerification(email: email)).called(1);
          expect(authController.loading.value, isFalse);
        });

        test('sets loading state during OTP request', () async {
          // Arrange
          const email = '<EMAIL>';
          final successResponse = ApiResponse.success(data: {});
          when(
            mockAuthService.sendEmailVerification(email: email),
          ).thenAnswer((_) async => successResponse);

          // Act
          final future = authController.requestOtp(email);

          // Assert
          expect(authController.loading.value, isTrue);

          await future;
          expect(authController.loading.value, isFalse);
        });
      });

      group('verifyOtp', () {
        test('returns true on successful OTP verification', () async {
          // Arrange
          const email = '<EMAIL>';
          const otp = '123456';
          final successResponse = ApiResponse.success(data: {});
          when(
            mockAuthService.validateOtp(email: email, otp: otp),
          ).thenAnswer((_) async => successResponse);

          // Act
          final result = await authController.verifyOtp(email, otp);

          // Assert
          expect(result, isTrue);
          expect(authController.loading.value, isFalse);
        });

        test('returns false on failed OTP verification', () async {
          // Arrange
          const email = '<EMAIL>';
          const otp = '000000';
          final errorResponse = ApiResponse.error(
            error: ApiError(message: 'Invalid OTP'),
          );
          when(
            mockAuthService.validateOtp(email: email, otp: otp),
          ).thenAnswer((_) async => errorResponse);

          // Act
          final result = await authController.verifyOtp(email, otp);

          // Assert
          expect(result, isFalse);
          expect(authController.loading.value, isFalse);
        });
      });

      group('registerWithPassword', () {
        test('calls setPassword API and updates user on success', () async {
          // Arrange
          const email = '<EMAIL>';
          const otp = '123456';
          const password = 'newPassword123';
          final userData = {
            'id': '1',
            'email': email,
            'name': 'Test User',
            'token': 'token',
          };
          final successResponse = ApiResponse.success(data: userData);

          when(
            mockAuthService.setPassword(
              email: email,
              otp: otp,
              password: password,
            ),
          ).thenAnswer((_) async => successResponse);

          // Act
          await authController.registerWithPassword(email, otp, password);

          // Assert
          verify(
            mockAuthService.setPassword(
              email: email,
              otp: otp,
              password: password,
            ),
          ).called(1);
          expect(authController.user.value, isNotNull);
          expect(authController.loading.value, isFalse);
        });

        test('sets loading state during password registration', () async {
          // Arrange
          const email = '<EMAIL>';
          const otp = '123456';
          const password = 'newPassword123';
          final successResponse = ApiResponse.success(data: {});

          when(
            mockAuthService.setPassword(
              email: email,
              otp: otp,
              password: password,
            ),
          ).thenAnswer((_) async => successResponse);

          // Act
          final future = authController.registerWithPassword(
            email,
            otp,
            password,
          );

          // Assert
          expect(authController.loading.value, isTrue);

          await future;
          expect(authController.loading.value, isFalse);
        });
      });
    });

    group('Profile Management', () {
      group('updateProfile', () {
        test('updates user profile with new information', () async {
          // Arrange
          const name = 'Updated Name';
          const university = 'Test University';
          const semester = '5th Semester';
          const branch = 'Computer Science';

          // Set initial user
          authController.user.value = User(
            id: '1',
            name: 'Old Name',
            email: '<EMAIL>',
            token: 'token',
          );

          // Act
          await authController.updateProfile(
            name: name,
            university: university,
            semester: semester,
            branch: branch,
          );

          // Assert
          expect(authController.user.value?.name, equals(name));
          expect(authController.user.value?.id, equals('1'));
          expect(authController.user.value?.email, equals('<EMAIL>'));
          expect(authController.user.value?.token, equals('token'));
          expect(authController.loading.value, isFalse);
        });

        test('sets loading state during profile update', () async {
          // Arrange
          const name = 'Updated Name';
          const university = 'Test University';
          const semester = '5th Semester';
          const branch = 'Computer Science';

          // Act
          final future = authController.updateProfile(
            name: name,
            university: university,
            semester: semester,
            branch: branch,
          );

          // Assert
          expect(authController.loading.value, isTrue);

          await future;
          expect(authController.loading.value, isFalse);
        });

        test('handles null user gracefully during profile update', () async {
          // Arrange
          authController.user.value = null;

          // Act
          await authController.updateProfile(
            name: 'Name',
            university: 'University',
            semester: 'Semester',
            branch: 'Branch',
          );

          // Assert
          expect(authController.user.value?.name, equals('Name'));
          expect(authController.user.value?.id, equals(''));
          expect(authController.user.value?.email, equals(''));
          expect(authController.user.value?.token, equals(''));
        });
      });
    });

    group('Logout', () {
      test('calls logout on both services and clears user', () async {
        // Arrange
        authController.user.value = User(
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          token: 'token',
        );

        when(mockSocialAuthService.logout()).thenAnswer((_) async {});
        when(
          mockAuthService.signout(),
        ).thenAnswer((_) async => ApiResponse.success());

        // Act
        await authController.logout();

        // Assert
        verify(mockSocialAuthService.logout()).called(1);
        verify(mockAuthService.signout()).called(1);
        expect(authController.user.value, isNull);
      });

      test('handles logout errors gracefully', () async {
        // Arrange
        authController.user.value = User(
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          token: 'token',
        );

        when(
          mockSocialAuthService.logout(),
        ).thenThrow(Exception('Logout failed'));
        when(
          mockAuthService.signout(),
        ).thenAnswer((_) async => ApiResponse.success());

        // Act & Assert
        expect(() => authController.logout(), returnsNormally);
        expect(authController.user.value, isNull);
      });
    });

    group('State Management', () {
      test('user is reactive and notifies listeners', () {
        // Arrange
        User? capturedUser;
        authController.user.listen((user) {
          capturedUser = user;
        });

        final testUser = User(
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          token: 'token',
        );

        // Act
        authController.user.value = testUser;

        // Assert
        expect(capturedUser, equals(testUser));
      });

      test('loading states are independent and reactive', () {
        // Arrange
        bool? capturedLoading;
        bool? capturedGoogleLoading;
        bool? capturedFacebookLoading;
        bool? capturedMicrosoftLoading;

        authController.loading.listen((loading) {
          capturedLoading = loading;
        });
        authController.googleLoading.listen((loading) {
          capturedGoogleLoading = loading;
        });
        authController.facebookLoading.listen((loading) {
          capturedFacebookLoading = loading;
        });
        authController.microsoftLoading.listen((loading) {
          capturedMicrosoftLoading = loading;
        });

        // Act
        authController.loading.value = true;
        authController.googleLoading.value = true;
        authController.facebookLoading.value = true;
        authController.microsoftLoading.value = true;

        // Assert
        expect(capturedLoading, isTrue);
        expect(capturedGoogleLoading, isTrue);
        expect(capturedFacebookLoading, isTrue);
        expect(capturedMicrosoftLoading, isTrue);
      });
    });

    group('Error Handling', () {
      test('handles service exceptions gracefully', () async {
        // Arrange
        when(
          mockAuthService.sendEmailVerification(email: anyNamed('email')),
        ).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(
          () => authController.requestOtp('<EMAIL>'),
          returnsNormally,
        );
        expect(authController.loading.value, isFalse);
      });

      test('resets loading states on exceptions', () async {
        // Arrange
        when(
          mockSocialAuthService.signInWithGoogle(),
        ).thenThrow(Exception('Google error'));

        // Act
        await authController.signInWithGoogle();

        // Assert
        expect(authController.googleLoading.value, isFalse);
      });
    });
  });
}
