import 'package:firebase_auth/firebase_auth.dart' as firebase;
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:somayya_academy/app/core/models/api_response.dart';
import 'package:somayya_academy/app/features/auth/services/auth_service.dart';
import 'package:somayya_academy/app/features/auth/services/social_auth_service.dart';

import '../../../../helpers/firebase_helpers.dart';
import 'social_auth_service_test.mocks.dart';

// Generate mocks for social auth dependencies
@GenerateMocks([
  firebase.FirebaseAuth,
  firebase.User,
  firebase.UserCredential,
  firebase.UserInfo,
  GoogleSignIn,
  GoogleSignInAccount,
  GoogleSignInAuthentication,
  FacebookAuth,
  LoginResult,
  AccessToken,
  AuthService,
])
void main() {
  group('SocialAuthService', () {
    late SocialAuthService socialAuthService;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockGoogleSignIn mockGoogleSignIn;
    late MockFacebookAuth mockFacebookAuth;
    late MockAuthService mockAuthService;
    late MockGoogleSignInAccount mockGoogleSignInAccount;
    late MockGoogleSignInAuthentication mockGoogleSignInAuthentication;
    late MockUserCredential mockUserCredential;
    late MockUser mockFirebaseUser;
    late MockUserInfo mockUserInfo;
    late MockLoginResult mockFacebookLoginResult;
    late MockAccessToken mockAccessToken;

    setUp(() {
      // Setup Firebase mocks
      setupFirebaseCoreMocks();
      
      // Clear GetX dependencies
      Get.reset();
      
      // Create mocks
      mockFirebaseAuth = MockFirebaseAuth();
      mockGoogleSignIn = MockGoogleSignIn();
      mockFacebookAuth = MockFacebookAuth();
      mockAuthService = MockAuthService();
      mockGoogleSignInAccount = MockGoogleSignInAccount();
      mockGoogleSignInAuthentication = MockGoogleSignInAuthentication();
      mockUserCredential = MockUserCredential();
      mockFirebaseUser = MockUser();
      mockUserInfo = MockUserInfo();
      mockFacebookLoginResult = MockLoginResult();
      mockAccessToken = MockAccessToken();
      
      // Register mocked AuthService
      Get.put<AuthService>(mockAuthService);
      
      socialAuthService = SocialAuthService();
    });

    tearDown() {
      Get.reset();
    });

    group('signInWithGoogle', () {
      test('returns success response on successful Google sign-in', () async {
        // Arrange
        const email = '<EMAIL>';
        const uid = 'google-uid-123';
        const idToken = 'google-id-token';
        
        final successResponse = ApiResponse.success(
          data: {'token': 'api-token', 'user': {'id': '1', 'email': email}},
        );

        // Setup Google Sign-In mocks
        when(mockGoogleSignIn.authenticate())
            .thenAnswer((_) async => mockGoogleSignInAccount);
        when(mockGoogleSignInAccount.authentication)
            .thenReturn(mockGoogleSignInAuthentication);
        when(mockGoogleSignInAuthentication.idToken).thenReturn(idToken);
        
        // Setup Firebase Auth mocks
        when(mockFirebaseAuth.signInWithCredential(any))
            .thenAnswer((_) async => mockUserCredential);
        when(mockUserCredential.user).thenReturn(mockFirebaseUser);
        when(mockFirebaseUser.providerData).thenReturn([mockUserInfo]);
        when(mockUserInfo.email).thenReturn(email);
        when(mockUserInfo.uid).thenReturn(uid);
        
        // Setup AuthService mock
        when(mockAuthService.socialSignin(
          email: email,
          providerName: 'google',
          socialUserId: uid,
        )).thenAnswer((_) async => successResponse);

        // Act
        final result = await socialAuthService.signInWithGoogle();

        // Assert
        expect(result, isNotNull);
        expect(result!.success, isTrue);
        expect(result.data, isNotNull);
        verify(mockAuthService.socialSignin(
          email: email,
          providerName: 'google',
          socialUserId: uid,
        )).called(1);
      });

      test('throws exception when Google sign-in fails', () async {
        // Arrange
        when(mockGoogleSignIn.authenticate())
            .thenThrow(Exception('Google sign-in failed'));

        // Act & Assert
        expect(
          () => socialAuthService.signInWithGoogle(),
          throwsA(isA<Exception>()),
        );
      });

      test('throws exception when Firebase user is null after Google sign-in', () async {
        // Arrange
        const idToken = 'google-id-token';
        
        when(mockGoogleSignIn.authenticate())
            .thenAnswer((_) async => mockGoogleSignInAccount);
        when(mockGoogleSignInAccount.authentication)
            .thenReturn(mockGoogleSignInAuthentication);
        when(mockGoogleSignInAuthentication.idToken).thenReturn(idToken);
        when(mockFirebaseAuth.signInWithCredential(any))
            .thenAnswer((_) async => mockUserCredential);
        when(mockUserCredential.user).thenReturn(null);

        // Act & Assert
        expect(
          () => socialAuthService.signInWithGoogle(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('signInWithFacebook', () {
      test('returns success response on successful Facebook sign-in', () async {
        // Arrange
        const email = '<EMAIL>';
        const uid = 'facebook-uid-123';
        const accessToken = 'facebook-access-token';
        
        final successResponse = ApiResponse.success(
          data: {'token': 'api-token', 'user': {'id': '1', 'email': email}},
        );

        // Setup Facebook Auth mocks
        when(mockFacebookAuth.login()).thenAnswer((_) async => mockFacebookLoginResult);
        when(mockFacebookLoginResult.status).thenReturn(LoginStatus.success);
        when(mockFacebookLoginResult.accessToken).thenReturn(mockAccessToken);
        when(mockAccessToken.tokenString).thenReturn(accessToken);
        
        // Setup Firebase Auth mocks
        when(mockFirebaseAuth.signInWithCredential(any))
            .thenAnswer((_) async => mockUserCredential);
        when(mockUserCredential.user).thenReturn(mockFirebaseUser);
        when(mockFirebaseUser.providerData).thenReturn([mockUserInfo]);
        when(mockUserInfo.email).thenReturn(email);
        when(mockUserInfo.uid).thenReturn(uid);
        
        // Setup AuthService mock
        when(mockAuthService.socialSignin(
          email: email,
          providerName: 'facebook',
          socialUserId: uid,
        )).thenAnswer((_) async => successResponse);

        // Act
        final result = await socialAuthService.signInWithFacebook();

        // Assert
        expect(result, isNotNull);
        expect(result!.success, isTrue);
        expect(result.data, isNotNull);
        verify(mockAuthService.socialSignin(
          email: email,
          providerName: 'facebook',
          socialUserId: uid,
        )).called(1);
      });

      test('throws exception when Facebook login is cancelled', () async {
        // Arrange
        when(mockFacebookAuth.login()).thenAnswer((_) async => mockFacebookLoginResult);
        when(mockFacebookLoginResult.status).thenReturn(LoginStatus.cancelled);

        // Act & Assert
        expect(
          () => socialAuthService.signInWithFacebook(),
          throwsA(isA<Exception>()),
        );
      });

      test('throws exception when Facebook login fails', () async {
        // Arrange
        when(mockFacebookAuth.login()).thenAnswer((_) async => mockFacebookLoginResult);
        when(mockFacebookLoginResult.status).thenReturn(LoginStatus.failed);

        // Act & Assert
        expect(
          () => socialAuthService.signInWithFacebook(),
          throwsA(isA<Exception>()),
        );
      });

      test('throws exception when Facebook access token is null', () async {
        // Arrange
        when(mockFacebookAuth.login()).thenAnswer((_) async => mockFacebookLoginResult);
        when(mockFacebookLoginResult.status).thenReturn(LoginStatus.success);
        when(mockFacebookLoginResult.accessToken).thenReturn(null);

        // Act & Assert
        expect(
          () => socialAuthService.signInWithFacebook(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('signInWithMicrosoft', () {
      test('returns success response on successful Microsoft sign-in', () async {
        // Arrange
        const email = '<EMAIL>';
        const uid = 'microsoft-uid-123';
        
        final successResponse = ApiResponse.success(
          data: {'token': 'api-token', 'user': {'id': '1', 'email': email}},
        );

        // Setup Firebase Auth mocks for Microsoft
        when(mockFirebaseAuth.signInWithCredential(any))
            .thenAnswer((_) async => mockUserCredential);
        when(mockUserCredential.user).thenReturn(mockFirebaseUser);
        when(mockFirebaseUser.providerData).thenReturn([mockUserInfo]);
        when(mockUserInfo.email).thenReturn(email);
        when(mockUserInfo.uid).thenReturn(uid);
        
        // Setup AuthService mock
        when(mockAuthService.socialSignin(
          email: email,
          providerName: 'microsoft',
          socialUserId: uid,
        )).thenAnswer((_) async => successResponse);

        // Act
        final result = await socialAuthService.signInWithMicrosoft();

        // Assert
        expect(result, isNotNull);
        expect(result!.success, isTrue);
        expect(result.data, isNotNull);
        verify(mockAuthService.socialSignin(
          email: email,
          providerName: 'microsoft',
          socialUserId: uid,
        )).called(1);
      });

      test('throws exception when Microsoft sign-in fails', () async {
        // Arrange
        when(mockFirebaseAuth.signInWithCredential(any))
            .thenThrow(firebase.FirebaseAuthException(
          code: 'account-exists-with-different-credential',
          message: 'Account exists with different credential',
        ));

        // Act & Assert
        expect(
          () => socialAuthService.signInWithMicrosoft(),
          throwsA(isA<firebase.FirebaseAuthException>()),
        );
      });
    });

    group('_signInFromSocial', () {
      test('calls AuthService.socialSignin with correct parameters', () async {
        // Arrange
        const email = '<EMAIL>';
        const providerName = 'google';
        const socialUserId = 'social-123';
        
        final successResponse = ApiResponse.success(
          data: {'token': 'api-token'},
        );

        when(mockAuthService.socialSignin(
          email: email,
          providerName: providerName,
          socialUserId: socialUserId,
        )).thenAnswer((_) async => successResponse);

        // Act
        // Note: _signInFromSocial is private, so we test it through public methods
        // This is tested indirectly through the social sign-in methods above
        
        // Assert
        expect(true, isTrue); // Placeholder - tested through integration
      });
    });

    group('logout', () {
      test('signs out from all providers and clears auth data', () async {
        // Arrange
        when(mockGoogleSignIn.signOut()).thenAnswer((_) async => null);
        when(mockFacebookAuth.logOut()).thenAnswer((_) async => {});
        when(mockFirebaseAuth.signOut()).thenAnswer((_) async {});
        when(mockAuthService.clearAuthData()).thenAnswer((_) async {});

        // Act
        await socialAuthService.logout();

        // Assert
        verify(mockGoogleSignIn.signOut()).called(1);
        verify(mockFacebookAuth.logOut()).called(1);
        verify(mockFirebaseAuth.signOut()).called(1);
        verify(mockAuthService.clearAuthData()).called(1);
      });

      test('handles logout errors gracefully', () async {
        // Arrange
        when(mockGoogleSignIn.signOut()).thenThrow(Exception('Google signout failed'));
        when(mockFacebookAuth.logOut()).thenAnswer((_) async => {});
        when(mockFirebaseAuth.signOut()).thenAnswer((_) async {});
        when(mockAuthService.clearAuthData()).thenAnswer((_) async {});

        // Act & Assert
        // Should not throw even if one provider fails
        expect(() => socialAuthService.logout(), returnsNormally);
      });
    });

    group('Service Lifecycle', () {
      test('can be instantiated as GetxService', () {
        // Act
        final service = SocialAuthService();

        // Assert
        expect(service, isA<GetxService>());
        expect(service, isA<SocialAuthService>());
      });

      test('can be registered and retrieved from GetX', () {
        // Arrange
        Get.put<SocialAuthService>(SocialAuthService());

        // Act
        final retrievedService = Get.find<SocialAuthService>();

        // Assert
        expect(retrievedService, isA<SocialAuthService>());
        expect(retrievedService, isNotNull);
      });
    });
  });
}
