import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_core_platform_interface/firebase_core_platform_interface.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:somayya_academy/app/core/config/app_config.dart';
import 'package:somayya_academy/app/core/network/services/firebase_core_service.dart';
import 'package:somayya_academy/app/core/firebase/firebase_options_dev.dart' as dev_options;
import 'package:somayya_academy/app/core/firebase/firebase_options.dart' as prod_options;

import '../../../../helpers/firebase_helpers.dart';

void main() {
  group('FirebaseService', () {
    late FirebaseOptions? capturedOptions;

    setUp(() {
      // Reset mocks and service state before each test
      FirebaseService.reset();
      capturedOptions = null;
      AppConfig.resetFlavor();

      // Setup mock to capture options by default
      setupFirebaseCoreMocksWithCapture((options) {
        capturedOptions = options;
      });
    });

    test('initializes with dev options in dev flavor', () async {
      AppConfig.setTestFlavor(AppFlavor.dev);
      await FirebaseService.initialize();

      expect(FirebaseService.isInitialized, isTrue);
      expect(capturedOptions, dev_options.DefaultFirebaseOptions.currentPlatform);
    });

    test('initializes with prod options in prod flavor', () async {
      AppConfig.setTestFlavor(AppFlavor.prod);
      await FirebaseService.initialize();

      expect(FirebaseService.isInitialized, isTrue);
      expect(capturedOptions, prod_options.DefaultFirebaseOptions.currentPlatform);
    });

    test('does not re-initialize if already initialized', () async {
      // First initialization
      AppConfig.setTestFlavor(AppFlavor.dev);
      await FirebaseService.initialize();

      // Attempt to re-initialize with a different flavor
      capturedOptions = null; // Reset capture
      AppConfig.setTestFlavor(AppFlavor.prod);
      await FirebaseService.initialize();

      // Should not have changed the options because it's already initialized
      expect(capturedOptions, isNull);
    });

    test('isInitialized returns correct status', () async {
      expect(FirebaseService.isInitialized, isFalse);
      AppConfig.setTestFlavor(AppFlavor.dev);
      await FirebaseService.initialize();
      expect(FirebaseService.isInitialized, isTrue);
    });

    test('handles initialization failure', () async {
      // Setup mock to throw an exception for this specific test
      setupFirebaseCoreMocksWithFailure();

      AppConfig.setTestFlavor(AppFlavor.dev);

      expect(
        () async => await FirebaseService.initialize(),
        throwsA(isA<Exception>()),
      );
      expect(FirebaseService.isInitialized, isFalse);
    });
  });
}
